#!/usr/bin/env python3
"""
为HSI-RefSR创建适配的数据集
从DaoGu HSI数据制作伪RGB图像，并进行偏移操作模拟相机不对齐
"""

import os
import numpy as np
from pathlib import Path
import cv2
from scipy.ndimage import shift
import random
from hdf5storage import savemat
import argparse

def parse_envi_header(header_file):
    """解析ENVI头文件"""
    header_info = {}
    with open(header_file, 'r') as f:
        lines = f.readlines()
    
    for line in lines:
        line = line.strip()
        if '=' in line and not line.startswith('#'):
            key, value = line.split('=', 1)
            key = key.strip()
            value = value.strip()
            header_info[key] = value
    
    return header_info

def read_envi_data(raw_file, header_info):
    """读取ENVI格式的HSI数据"""
    try:
        samples = int(header_info.get('samples', 0))
        lines = int(header_info.get('lines', 0))
        bands = int(header_info.get('bands', 0))
        data_type = int(header_info.get('data type', 12))
        interleave = header_info.get('interleave', 'bil').strip()
        
        # 数据类型映射
        dtype_map = {
            1: np.uint8, 2: np.int16, 3: np.int32, 4: np.float32, 
            5: np.float64, 12: np.uint16, 13: np.uint32
        }
        dtype = dtype_map.get(data_type, np.uint16)
        
        # 读取原始数据
        data = np.fromfile(raw_file, dtype=dtype)
        expected_size = samples * lines * bands
        
        if len(data) != expected_size:
            print(f"Warning: Data size mismatch! Got {len(data)}, expected {expected_size}")
            return None
        
        # 根据interleave格式重塑数据
        if interleave.lower() == 'bil':  # Band Interleaved by Line
            data = data.reshape(lines, bands, samples)
            data = data.transpose(1, 0, 2)  # (bands, lines, samples)
        elif interleave.lower() == 'bip':  # Band Interleaved by Pixel
            data = data.reshape(lines, samples, bands)
            data = data.transpose(2, 0, 1)  # (bands, lines, samples)
        else:  # BSQ - Band Sequential
            data = data.reshape(bands, lines, samples)
        
        return data.astype(np.float32)
        
    except Exception as e:
        print(f"Error reading HSI data: {e}")
        return None

def get_wavelengths(header_info):
    """从头文件中提取波长信息"""
    wavelength_str = header_info.get('Wavelength', '')
    if wavelength_str:
        try:
            wavelength_str = wavelength_str.replace('{', '').replace('}', '')
            wavelengths = []
            for w in wavelength_str.split(','):
                w = w.strip()
                if w:
                    wavelengths.append(float(w))
            if wavelengths:
                return np.array(wavelengths)
        except Exception as e:
            print(f"Error parsing wavelengths: {e}")
    return None

def hsi_to_rgb(hsi_data, wavelengths, method='band_selection'):
    """将HSI数据转换为RGB图像"""
    bands, height, width = hsi_data.shape

    if wavelengths is None or len(wavelengths) == 0:
        print("No wavelength information, using default band selection")
        # 如果没有波长信息，使用默认的波段选择
        r_band = min(int(bands * 0.8), bands - 1)  # 接近红光
        g_band = min(int(bands * 0.5), bands - 1)  # 接近绿光
        b_band = min(int(bands * 0.2), bands - 1)  # 接近蓝光

        rgb = np.stack([
            hsi_data[r_band],
            hsi_data[g_band],
            hsi_data[b_band]
        ], axis=2)
    else:
        print(f"Using wavelength-based selection. Wavelength range: {wavelengths.min():.1f}-{wavelengths.max():.1f} nm")

        if method == 'band_selection':
            # 基于波长的波段选择方法
            # 对于近红外/短波红外数据，调整目标波长
            if wavelengths.min() > 900:  # 如果是近红外/短波红外数据
                # 使用相对波长选择
                wl_range = wavelengths.max() - wavelengths.min()
                target_wavelengths = [
                    wavelengths.min() + wl_range * 0.8,  # 长波段作为"红"
                    wavelengths.min() + wl_range * 0.5,  # 中波段作为"绿"
                    wavelengths.min() + wl_range * 0.2   # 短波段作为"蓝"
                ]
            else:
                # 标准可见光波长
                target_wavelengths = [650, 550, 450]  # R, G, B的近似波长(nm)

            rgb_bands = []
            for target_wl in target_wavelengths:
                # 找到最接近目标波长的波段
                idx = np.argmin(np.abs(wavelengths - target_wl))
                rgb_bands.append(idx)

            print(f"Selected bands: R={rgb_bands[0]}({wavelengths[rgb_bands[0]]:.1f}nm), "
                  f"G={rgb_bands[1]}({wavelengths[rgb_bands[1]]:.1f}nm), "
                  f"B={rgb_bands[2]}({wavelengths[rgb_bands[2]]:.1f}nm)")

            rgb = np.stack([
                hsi_data[rgb_bands[0]],  # R
                hsi_data[rgb_bands[1]],  # G
                hsi_data[rgb_bands[2]]   # B
            ], axis=2)
        
        elif method == 'spectral_response':
            # 使用光谱响应函数方法（简化版）
            # 这里使用高斯函数模拟RGB传感器的光谱响应
            rgb_centers = [650, 550, 450]  # R, G, B中心波长
            rgb_widths = [80, 80, 80]      # 响应宽度
            
            rgb_channels = []
            for center, width in zip(rgb_centers, rgb_widths):
                # 计算高斯权重
                weights = np.exp(-0.5 * ((wavelengths - center) / width) ** 2)
                weights = weights / np.sum(weights)  # 归一化
                
                # 加权求和
                channel = np.sum(hsi_data * weights[:, None, None], axis=0)
                rgb_channels.append(channel)
            
            rgb = np.stack(rgb_channels, axis=2)
    
    # 归一化到0-1范围
    rgb = (rgb - rgb.min()) / (rgb.max() - rgb.min() + 1e-8)
    return rgb

def apply_random_offset(image, max_offset=20):
    """对图像应用随机偏移"""
    h, w = image.shape[:2]
    
    # 生成随机偏移量
    offset_x = random.randint(-max_offset, max_offset)
    offset_y = random.randint(-max_offset, max_offset)
    
    # 创建变换矩阵
    M = np.float32([[1, 0, offset_x], [0, 1, offset_y]])
    
    # 应用仿射变换
    if len(image.shape) == 3:
        shifted = cv2.warpAffine(image, M, (w, h), borderMode=cv2.BORDER_REFLECT)
    else:
        shifted = cv2.warpAffine(image, M, (w, h), borderMode=cv2.BORDER_REFLECT)
    
    return shifted, (offset_x, offset_y)

def downsample_image(image, scale_factor=4):
    """下采样图像"""
    h, w = image.shape[:2]
    new_h, new_w = h // scale_factor, w // scale_factor
    
    if len(image.shape) == 3:
        downsampled = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
    else:
        downsampled = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
    
    return downsampled

def process_hsi_sample(hsi_file, header_file, output_dir, sample_name, scale_factor=4):
    """处理单个HSI样本"""
    print(f"Processing {sample_name}...")
    
    # 读取HSI数据
    header_info = parse_envi_header(header_file)
    hsi_data = read_envi_data(hsi_file, header_info)
    
    if hsi_data is None:
        print(f"Failed to read HSI data for {sample_name}")
        return False
    
    # 获取波长信息
    wavelengths = get_wavelengths(header_info)
    
    # 转换为RGB
    rgb_image = hsi_to_rgb(hsi_data, wavelengths, method='band_selection')
    
    # 归一化HSI数据
    hsi_normalized = (hsi_data - hsi_data.min()) / (hsi_data.max() - hsi_data.min() + 1e-8)
    
    # 创建偏移的RGB图像（高分辨率）
    rgb_shifted, offset = apply_random_offset(rgb_image, max_offset=20)
    
    # 下采样HSI伪RGB图像（低分辨率）
    rgb_lr = downsample_image(rgb_image, scale_factor)
    
    # 下采样HSI数据
    hsi_lr = np.zeros((hsi_normalized.shape[0], 
                       hsi_normalized.shape[1] // scale_factor,
                       hsi_normalized.shape[2] // scale_factor))
    
    for i in range(hsi_normalized.shape[0]):
        hsi_lr[i] = downsample_image(hsi_normalized[i], scale_factor)
    
    # 保存数据
    output_dir = Path(output_dir)
    
    # 保存HSI数据 (HR和LR)
    hsi_hr_dir = output_dir / "img1_hsi" / "HR"
    hsi_hr_dir.mkdir(parents=True, exist_ok=True)
    savemat(str(hsi_hr_dir / f"{sample_name}.mat"), {'gt': hsi_normalized.transpose(1, 2, 0)})
    
    # 保存HSI RGB图像 (HR和LR)
    hsi_rgb_hr_dir = output_dir / "img1" / "HR"
    hsi_rgb_hr_dir.mkdir(parents=True, exist_ok=True)
    cv2.imwrite(str(hsi_rgb_hr_dir / f"{sample_name}.png"), 
                (rgb_image * 255).astype(np.uint8))
    
    # 保存偏移的RGB图像 (HR)
    rgb_hr_dir = output_dir / "img2" / "HR"
    rgb_hr_dir.mkdir(parents=True, exist_ok=True)
    cv2.imwrite(str(rgb_hr_dir / f"{sample_name}.png"), 
                (rgb_shifted * 255).astype(np.uint8))
    
    print(f"Saved {sample_name} with offset: {offset}")
    return True

def find_hsi_samples(daogu_dir):
    """查找所有HSI样本"""
    daogu_path = Path(daogu_dir)
    samples = []
    
    for exp_dir in daogu_path.iterdir():
        if exp_dir.is_dir() and exp_dir.name.startswith("实验"):
            for sample_dir in exp_dir.iterdir():
                if sample_dir.is_dir():
                    capture_dir = sample_dir / "capture"
                    if capture_dir.exists():
                        # 查找主要的HSI文件（排除校准文件）
                        for hdr_file in capture_dir.glob("*.hdr"):
                            if not hdr_file.name.startswith(("DARKREF", "WHITEREF", "REFLECTANCE")):
                                raw_file = capture_dir / (hdr_file.stem + ".raw")
                                if raw_file.exists():
                                    sample_name = f"{exp_dir.name}_{sample_dir.name}"
                                    samples.append({
                                        'name': sample_name,
                                        'hdr': hdr_file,
                                        'raw': raw_file
                                    })
    
    return samples

def create_name_files(output_dir, sample_names):
    """创建训练和测试的名称文件"""
    output_dir = Path(output_dir)

    # 简单分割：80%训练，20%测试
    random.shuffle(sample_names)
    split_idx = int(len(sample_names) * 0.8)

    train_names = sample_names[:split_idx]
    test_names = sample_names[split_idx:]

    # 写入文件，使用UTF-8编码
    with open(output_dir / "train.txt", 'w', encoding='utf-8') as f:
        for name in train_names:
            f.write(f"{name}\n")

    with open(output_dir / "test.txt", 'w', encoding='utf-8') as f:
        for name in test_names:
            f.write(f"{name}\n")

    print(f"Created train.txt with {len(train_names)} samples")
    print(f"Created test.txt with {len(test_names)} samples")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Create dataset for HSI-RefSR')
    parser.add_argument('--daogu_dir', default='DaoGu', help='DaoGu dataset directory')
    parser.add_argument('--output_dir', default='data/daogu', help='Output directory')
    parser.add_argument('--scale_factor', type=int, default=4, help='Downsampling scale factor')
    parser.add_argument('--max_samples', type=int, default=None, help='Maximum number of samples to process')
    
    args = parser.parse_args()
    
    print("=== Creating HSI-RefSR Dataset from DaoGu ===")
    
    # 查找所有HSI样本
    samples = find_hsi_samples(args.daogu_dir)
    print(f"Found {len(samples)} HSI samples")
    
    if args.max_samples:
        samples = samples[:args.max_samples]
        print(f"Processing first {len(samples)} samples")
    
    # 处理样本
    processed_samples = []
    for sample in samples:
        success = process_hsi_sample(
            sample['raw'], 
            sample['hdr'], 
            args.output_dir, 
            sample['name'],
            args.scale_factor
        )
        if success:
            processed_samples.append(sample['name'])
    
    # 创建名称文件
    if processed_samples:
        create_name_files(args.output_dir, processed_samples)
        print(f"\n=== Dataset creation completed ===")
        print(f"Processed {len(processed_samples)} samples")
        print(f"Output directory: {args.output_dir}")
    else:
        print("No samples were processed successfully!")
