train:
    dataset:
        type@: refsr.SRFDataset
        input: img1
        ref: img2
        names_path: train.txt
        sf: 4
        crop_size:
        - 320
        - 512
        repeat: 5
        use_cache: true
        root: data/daogu
    loader:
        batch_size: 1
        shuffle: true
        num_workers: 1
        pin_memory: true
test:
    dataset:
        type@: refsr.SRFDataset
        input: img1
        ref: img2
        names_path: test.txt
        sf: 4
        use_cache: true
        root: data/daogu
    loader:
        batch_size: 1
        shuffle: false
engine:
    max_epochs: 500
    mnt_metric: val_psnr
    mnt_mode: max
    log_img_step: 10
    valid_log_img_step: 1
    pbar: qqdm
    save_per_epoch: 10
    enable_tensorboard: false
    log_step: 20
    valid_per_epoch: 1
    enable_early_stop: false
    early_stop_threshold: 0.01
    early_stop_count: 5
    num_fmt: '{:8.5g}'
    ckpt_save_mode: all
module:
    type@: refsr.CommonModule
    optimizer:
        type@: AdamW
        lr: 1.0e-05
        weight_decay: 5.0e-05
    model:
        type@: CrossNetHSI
        use_mask: true
        use_pwc: true
        reweight: false
