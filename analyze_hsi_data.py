#!/usr/bin/env python3
"""
分析DaoGu数据集中的HSI数据结构
"""

import os
import numpy as np
from pathlib import Path
import struct

def parse_envi_header(header_file):
    """解析ENVI头文件"""
    header_info = {}
    with open(header_file, 'r') as f:
        lines = f.readlines()
    
    for line in lines:
        line = line.strip()
        if '=' in line:
            key, value = line.split('=', 1)
            key = key.strip()
            value = value.strip()
            header_info[key] = value
    
    return header_info

def read_envi_data(raw_file, header_info):
    """读取ENVI格式的HSI数据"""
    # 从header中提取关键信息
    samples = int(header_info.get('samples', 0))
    lines = int(header_info.get('lines', 0))
    bands = int(header_info.get('bands', 0))
    data_type = int(header_info.get('data type', 0))
    interleave = header_info.get('interleave', 'bsq').strip()
    
    # 数据类型映射
    dtype_map = {
        1: np.uint8,
        2: np.int16,
        3: np.int32,
        4: np.float32,
        5: np.float64,
        12: np.uint16,
        13: np.uint32,
        14: np.int64,
        15: np.uint64
    }
    
    dtype = dtype_map.get(data_type, np.uint16)
    
    print(f"Data dimensions: {samples} x {lines} x {bands}")
    print(f"Data type: {dtype}")
    print(f"Interleave: {interleave}")
    
    # 读取原始数据
    try:
        data = np.fromfile(raw_file, dtype=dtype)
        expected_size = samples * lines * bands
        
        print(f"Read {len(data)} pixels, expected {expected_size}")
        
        if len(data) != expected_size:
            print(f"Warning: Data size mismatch!")
            return None
        
        # 根据interleave格式重塑数据
        if interleave.lower() == 'bil':  # Band Interleaved by Line
            data = data.reshape(lines, bands, samples)
            data = data.transpose(1, 0, 2)  # (bands, lines, samples)
        elif interleave.lower() == 'bip':  # Band Interleaved by Pixel
            data = data.reshape(lines, samples, bands)
            data = data.transpose(2, 0, 1)  # (bands, lines, samples)
        else:  # BSQ - Band Sequential
            data = data.reshape(bands, lines, samples)
        
        return data
        
    except Exception as e:
        print(f"Error reading data: {e}")
        return None

def analyze_hsi_sample():
    """分析一个HSI样本"""
    # 选择一个样本进行分析
    sample_dir = Path("DaoGu/实验1/benzi_2024-11-20_02-21-12/capture")
    header_file = sample_dir / "benzi_2024-11-20_02-21-12.hdr"
    raw_file = sample_dir / "benzi_2024-11-20_02-21-12.raw"
    
    if not header_file.exists() or not raw_file.exists():
        print("Sample files not found!")
        return None
    
    print(f"Analyzing: {raw_file}")
    print(f"File size: {raw_file.stat().st_size} bytes")
    
    # 解析头文件
    header_info = parse_envi_header(header_file)
    
    # 读取数据
    data = read_envi_data(raw_file, header_info)
    
    if data is not None:
        print(f"Final data shape: {data.shape}")
        print(f"Data range: {data.min()} - {data.max()}")
        print(f"Data mean: {data.mean():.2f}")
        
        # 提取波长信息
        wavelength_str = header_info.get('Wavelength', '')
        if wavelength_str:
            # 解析波长数组
            wavelength_str = wavelength_str.replace('{', '').replace('}', '')
            wavelengths = [float(w.strip()) for w in wavelength_str.split(',') if w.strip()]
            print(f"Wavelength range: {min(wavelengths):.2f} - {max(wavelengths):.2f} nm")
            print(f"Number of wavelengths: {len(wavelengths)}")
            
            return data, wavelengths
    
    return None, None

def list_all_samples():
    """列出所有可用的HSI样本"""
    daogu_dir = Path("DaoGu")
    samples = []
    
    for exp_dir in daogu_dir.iterdir():
        if exp_dir.is_dir() and exp_dir.name.startswith("实验"):
            for sample_dir in exp_dir.iterdir():
                if sample_dir.is_dir():
                    capture_dir = sample_dir / "capture"
                    if capture_dir.exists():
                        # 查找.hdr和.raw文件
                        hdr_files = list(capture_dir.glob("*.hdr"))
                        raw_files = list(capture_dir.glob("*.raw"))
                        
                        for hdr_file in hdr_files:
                            if not hdr_file.name.startswith(("DARKREF", "WHITEREF", "REFLECTANCE")):
                                raw_file = capture_dir / (hdr_file.stem + ".raw")
                                if raw_file.exists():
                                    samples.append({
                                        'experiment': exp_dir.name,
                                        'sample': sample_dir.name,
                                        'hdr': hdr_file,
                                        'raw': raw_file
                                    })
    
    return samples

if __name__ == "__main__":
    print("=== DaoGu HSI数据集分析 ===")
    
    # 列出所有样本
    samples = list_all_samples()
    print(f"\n找到 {len(samples)} 个HSI样本:")
    for i, sample in enumerate(samples[:5]):  # 只显示前5个
        print(f"{i+1}. {sample['experiment']}/{sample['sample']}")
    
    if len(samples) > 5:
        print(f"... 还有 {len(samples)-5} 个样本")
    
    # 分析第一个样本
    if samples:
        print(f"\n=== 分析样本: {samples[0]['experiment']}/{samples[0]['sample']} ===")
        data, wavelengths = analyze_hsi_sample()
        
        if data is not None:
            print("\n=== 数据分析完成 ===")
            print("数据格式: ENVI BIL")
            print(f"空间分辨率: {data.shape[2]} x {data.shape[1]}")
            print(f"光谱波段数: {data.shape[0]}")
            print(f"波长范围: 近红外到短波红外 (SWIR)")
        else:
            print("数据读取失败!")
    else:
        print("未找到HSI数据!")
