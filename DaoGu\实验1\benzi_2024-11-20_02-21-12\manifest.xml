<?xml version="1.0" encoding="utf-8"?>
<manifest>
 <file extension="raw" type="darkref">capture/DARKREF_benzi_2024-11-20_02-21-12.raw</file>
 <file extension="hdr" type="darkref">capture/DARKREF_benzi_2024-11-20_02-21-12.hdr</file>
 <file extension="raw" type="whiteref">capture/WHITEREF_benzi_2024-11-20_02-21-12.raw</file>
 <file extension="hdr" type="whiteref">capture/WHITEREF_benzi_2024-11-20_02-21-12.hdr</file>
 <file extension="raw" type="capture">capture/benzi_2024-11-20_02-21-12.raw</file>
 <file extension="hdr" type="capture">capture/benzi_2024-11-20_02-21-12.hdr</file>
 <file extension="xml" type="properties">metadata/benzi_2024-11-20_02-21-12.xml</file>
 <file extension="xsl" type="properties">metadata/benzi_2024-11-20_02-21-12.xsl</file>
 <file extension="png" type="preview">benzi_2024-11-20_02-21-12.png</file>
 <file extension="dat" type="reflectance">capture/REFLECTANCE_benzi_2024-11-20_02-21-12.dat</file>
 <file extension="hdr" type="reflectance">capture/REFLECTANCE_benzi_2024-11-20_02-21-12.hdr</file>
</manifest>
