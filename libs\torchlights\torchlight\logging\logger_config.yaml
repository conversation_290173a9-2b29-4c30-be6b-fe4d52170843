---
version: 1
disable_existing_loggers: false
formatters:
  colorlog:
    (): 'colorlog.ColoredFormatter'
    format: "%(log_color)s%(message)s"
    log_colors:
      WARNING: yellow
      ERROR: red
  datetime:
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: colorlog
  info_file_handler:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: datetime
    filename: info.log
    maxBytes: 10485760
    backupCount: 20
    encoding: utf8
  debug_file_handler:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: datetime
    filename: debug.log
    maxBytes: 10485760
    backupCount: 20
    encoding: utf8
loggers:
  Torchlight:
    level: DEBUG
    handlers:
      - console
      - info_file_handler
      - debug_file_handler
