ENVI
description = {
File Imported into ENVI}
file type = ENVI

sensor type = SWIR , Lumo - Scanner v2023-631
acquisition date = DATE(yyyy-mm-dd): 2024-12-17
Start Time = UTC TIME: 09:09:52
Stop Time = UTC TIME: 09:10:02

samples = 384
bands = 273
lines = 480

errors = {none}

interleave = bil
data type = 12
header offset = 0
byte order = 0
x start = 0
y start = 0
default bands = {12, 51, 71}

himg = {1, 384}
vimg = {1, 273}
hroi = {1, 384}
vroi = {1, 273}

fps = 50.00
fps_qpf = 50.05
tint = 2.979600
binning = {1, 1}
trigger mode = External
trigger sync = true
fodis = {0, 0}
sensorid = 471154
acquisitionwindow left = 0
acquisitionwindow top = 11
calibration pack = C:/Users/<USER>/Documents/Specim/471154_OLES15_20230417_calpack_BPR.scp

SWIR temperature = 149.00
Scb temperature channel1 = 21.15
Scb temperature channel2 = 25.92
Scb temperature channel3 = 25.68
Scb temperature channel4  = 26.38

temperature = {
149.00,
21.15,
25.92,
25.68,
26.38
}


Wavelength = {
996.35,
1001.94,
1007.53,
1013.12,
1018.70,
1024.29,
1029.88,
1035.46,
1041.05,
1046.63,
1052.22,
1057.80,
1063.38,
1068.96,
1074.54,
1080.12,
1085.70,
1091.28,
1096.86,
1102.44,
1108.01,
1113.59,
1119.17,
1124.74,
1130.31,
1135.89,
1141.46,
1147.03,
1152.60,
1158.17,
1163.74,
1169.31,
1174.88,
1180.45,
1186.02,
1191.58,
1197.15,
1202.72,
1208.28,
1213.84,
1219.41,
1224.97,
1230.53,
1236.09,
1241.66,
1247.22,
1252.78,
1258.34,
1263.89,
1269.45,
1275.01,
1280.57,
1286.12,
1291.68,
1297.24,
1302.79,
1308.35,
1313.90,
1319.45,
1325.00,
1330.56,
1336.11,
1341.66,
1347.21,
1352.76,
1358.31,
1363.86,
1369.41,
1374.95,
1380.50,
1386.05,
1391.60,
1397.14,
1402.69,
1408.23,
1413.78,
1419.32,
1424.86,
1430.41,
1435.95,
1441.49,
1447.03,
1452.58,
1458.12,
1463.66,
1469.20,
1474.74,
1480.27,
1485.81,
1491.35,
1496.89,
1502.43,
1507.96,
1513.50,
1519.04,
1524.57,
1530.11,
1535.64,
1541.18,
1546.71,
1552.24,
1557.78,
1563.31,
1568.84,
1574.37,
1579.91,
1585.44,
1590.97,
1596.50,
1602.03,
1607.56,
1613.09,
1618.62,
1624.15,
1629.68,
1635.20,
1640.73,
1646.26,
1651.79,
1657.31,
1662.84,
1668.37,
1673.89,
1679.42,
1684.95,
1690.47,
1696.00,
1701.52,
1707.04,
1712.57,
1718.09,
1723.62,
1729.14,
1734.66,
1740.18,
1745.71,
1751.23,
1756.75,
1762.27,
1767.79,
1773.32,
1778.84,
1784.36,
1789.88,
1795.40,
1800.92,
1806.44,
1811.96,
1817.48,
1823.00,
1828.52,
1834.04,
1839.55,
1845.07,
1850.59,
1856.11,
1861.63,
1867.14,
1872.66,
1878.18,
1883.70,
1889.21,
1894.73,
1900.25,
1905.76,
1911.28,
1916.80,
1922.31,
1927.83,
1933.35,
1938.86,
1944.38,
1949.89,
1955.41,
1960.92,
1966.44,
1971.95,
1977.47,
1982.99,
1988.50,
1994.01,
1999.53,
2005.04,
2010.56,
2016.07,
2021.59,
2027.10,
2032.62,
2038.13,
2043.65,
2049.16,
2054.67,
2060.19,
2065.70,
2071.22,
2076.73,
2082.24,
2087.76,
2093.27,
2098.79,
2104.30,
2109.81,
2115.33,
2120.84,
2126.36,
2131.87,
2137.38,
2142.90,
2148.41,
2153.92,
2159.44,
2164.95,
2170.47,
2175.98,
2181.50,
2187.01,
2192.52,
2198.04,
2203.55,
2209.07,
2214.58,
2220.10,
2225.61,
2231.12,
2236.64,
2242.15,
2247.67,
2253.18,
2258.70,
2264.21,
2269.73,
2275.25,
2280.76,
2286.28,
2291.79,
2297.31,
2302.82,
2308.34,
2313.86,
2319.37,
2324.89,
2330.41,
2335.92,
2341.44,
2346.96,
2352.47,
2357.99,
2363.51,
2369.03,
2374.55,
2380.06,
2385.58,
2391.10,
2396.62,
2402.14,
2407.66,
2413.18,
2418.70,
2424.21,
2429.73,
2435.25,
2440.78,
2446.30,
2451.82,
2457.34,
2462.86,
2468.38,
2473.90,
2479.42,
2484.95,
2490.47,
2495.99,
2501.51
}

fwhm = {
5.59,
5.59,
5.59,
5.59,
5.59,
5.59,
5.59,
5.59,
5.59,
5.58,
5.58,
5.58,
5.58,
5.58,
5.58,
5.58,
5.58,
5.58,
5.58,
5.58,
5.58,
5.58,
5.58,
5.57,
5.57,
5.57,
5.57,
5.57,
5.57,
5.57,
5.57,
5.57,
5.57,
5.57,
5.57,
5.57,
5.57,
5.57,
5.56,
5.56,
5.56,
5.56,
5.56,
5.56,
5.56,
5.56,
5.56,
5.56,
5.56,
5.56,
5.56,
5.56,
5.56,
5.56,
5.56,
5.55,
5.55,
5.55,
5.55,
5.55,
5.55,
5.55,
5.55,
5.55,
5.55,
5.55,
5.55,
5.55,
5.55,
5.55,
5.55,
5.55,
5.55,
5.55,
5.54,
5.54,
5.54,
5.54,
5.54,
5.54,
5.54,
5.54,
5.54,
5.54,
5.54,
5.54,
5.54,
5.54,
5.54,
5.54,
5.54,
5.54,
5.54,
5.54,
5.54,
5.54,
5.54,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.53,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.51,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52,
5.52
}
