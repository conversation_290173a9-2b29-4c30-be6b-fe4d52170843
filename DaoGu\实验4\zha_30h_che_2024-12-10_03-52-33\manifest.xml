<?xml version="1.0" encoding="utf-8"?>
<manifest>
 <file type="darkref" extension="raw">capture/DARKREF_zha_30h_che_2024-12-10_03-52-33.raw</file>
 <file type="darkref" extension="hdr">capture/DARKREF_zha_30h_che_2024-12-10_03-52-33.hdr</file>
 <file type="whiteref" extension="raw">capture/WHITEREF_zha_30h_che_2024-12-10_03-52-33.raw</file>
 <file type="whiteref" extension="hdr">capture/WHITEREF_zha_30h_che_2024-12-10_03-52-33.hdr</file>
 <file type="capture" extension="raw">capture/zha_30h_che_2024-12-10_03-52-33.raw</file>
 <file type="capture" extension="hdr">capture/zha_30h_che_2024-12-10_03-52-33.hdr</file>
 <file type="properties" extension="xml">metadata/zha_30h_che_2024-12-10_03-52-33.xml</file>
 <file type="properties" extension="xsl">metadata/zha_30h_che_2024-12-10_03-52-33.xsl</file>
 <file type="preview" extension="png">zha_30h_che_2024-12-10_03-52-33.png</file>
 <file type="reflectance" extension="dat">capture/REFLECTANCE_zha_30h_che_2024-12-10_03-52-33.dat</file>
 <file type="reflectance" extension="hdr">capture/REFLECTANCE_zha_30h_che_2024-12-10_03-52-33.hdr</file>
</manifest>
