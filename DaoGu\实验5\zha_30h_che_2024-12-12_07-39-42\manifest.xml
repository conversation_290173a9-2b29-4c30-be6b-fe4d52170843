<?xml version="1.0" encoding="utf-8"?>
<manifest>
 <file extension="raw" type="darkref">capture/DARKREF_zha_30h_che_2024-12-12_07-39-42.raw</file>
 <file extension="hdr" type="darkref">capture/DARKREF_zha_30h_che_2024-12-12_07-39-42.hdr</file>
 <file extension="raw" type="whiteref">capture/WHITEREF_zha_30h_che_2024-12-12_07-39-42.raw</file>
 <file extension="hdr" type="whiteref">capture/WHITEREF_zha_30h_che_2024-12-12_07-39-42.hdr</file>
 <file extension="raw" type="capture">capture/zha_30h_che_2024-12-12_07-39-42.raw</file>
 <file extension="hdr" type="capture">capture/zha_30h_che_2024-12-12_07-39-42.hdr</file>
 <file extension="xml" type="properties">metadata/zha_30h_che_2024-12-12_07-39-42.xml</file>
 <file extension="xsl" type="properties">metadata/zha_30h_che_2024-12-12_07-39-42.xsl</file>
 <file extension="png" type="preview">zha_30h_che_2024-12-12_07-39-42.png</file>
 <file extension="dat" type="reflectance">capture/REFLECTANCE_zha_30h_che_2024-12-12_07-39-42.dat</file>
 <file extension="hdr" type="reflectance">capture/REFLECTANCE_zha_30h_che_2024-12-12_07-39-42.hdr</file>
</manifest>
